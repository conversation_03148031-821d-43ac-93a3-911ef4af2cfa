{"id": "b57ae172-f96d-41d9-9ae7-b1cef4cc977d", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.components": {"name": "components", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "component_slug": {"name": "component_slug", "type": "text", "primaryKey": false, "notNull": true}, "code": {"name": "code", "type": "text", "primaryKey": false, "notNull": false}, "compiled_css": {"name": "compiled_css", "type": "text", "primaryKey": false, "notNull": false}, "component_names": {"name": "component_names", "type": "json", "primaryKey": false, "notNull": true}, "demo_code": {"name": "demo_code", "type": "text", "primaryKey": false, "notNull": false}, "demo_dependencies": {"name": "demo_dependencies", "type": "json", "primaryKey": false, "notNull": false}, "demo_direct_registry_dependencies": {"name": "demo_direct_registry_dependencies", "type": "json", "primaryKey": false, "notNull": false}, "dependencies": {"name": "dependencies", "type": "json", "primaryKey": false, "notNull": false}, "direct_registry_dependencies": {"name": "direct_registry_dependencies", "type": "json", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "global_css_extension": {"name": "global_css_extension", "type": "text", "primaryKey": false, "notNull": false}, "tailwind_config_extension": {"name": "tailwind_config_extension", "type": "text", "primaryKey": false, "notNull": false}, "downloads_count": {"name": "downloads_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "likes_count": {"name": "likes_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "is_public": {"name": "is_public", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "is_paid": {"name": "is_paid", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "payment_url": {"name": "payment_url", "type": "text", "primaryKey": false, "notNull": false}, "preview_url": {"name": "preview_url", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"components_component_slug_unique": {"name": "components_component_slug_unique", "nullsNotDistinct": false, "columns": ["component_slug"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.project": {"name": "project", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "template_type": {"name": "template_type", "type": "text", "primaryKey": false, "notNull": true}, "url": {"name": "url", "type": "text", "primaryKey": false, "notNull": false}, "git_url": {"name": "git_url", "type": "text", "primaryKey": false, "notNull": false}, "git_branch": {"name": "git_branch", "type": "text", "primaryKey": false, "notNull": false}, "preview_image_url": {"name": "preview_image_url", "type": "text", "primaryKey": false, "notNull": false}, "production_deploy_url": {"name": "production_deploy_url", "type": "text", "primaryKey": false, "notNull": false}, "workflow_id": {"name": "workflow_id", "type": "text", "primaryKey": false, "notNull": false}, "deployment_status": {"name": "deployment_status", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "custom_domain": {"name": "custom_domain", "type": "text", "primaryKey": false, "notNull": false}, "custom_domain_status": {"name": "custom_domain_status", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "custom_domain_verified_at": {"name": "custom_domain_verified_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "custom_hostname_id": {"name": "custom_hostname_id", "type": "text", "primaryKey": false, "notNull": false}, "ownership_verification": {"name": "ownership_verification", "type": "text", "primaryKey": false, "notNull": false}, "ssl_status": {"name": "ssl_status", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "visibility": {"name": "visibility", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "organization_id": {"name": "organization_id", "type": "text", "primaryKey": false, "notNull": true}, "container_id": {"name": "container_id", "type": "text", "primaryKey": false, "notNull": false}, "initial_message": {"name": "initial_message", "type": "text", "primaryKey": false, "notNull": false}, "knowledge": {"name": "knowledge", "type": "text", "primaryKey": false, "notNull": false}, "message_history": {"name": "message_history", "type": "text", "primaryKey": false, "notNull": true, "default": "'[]'"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"project_id_unique": {"name": "project_id_unique", "nullsNotDistinct": false, "columns": ["id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.project_ai_usage": {"name": "project_ai_usage", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "project_id": {"name": "project_id", "type": "text", "primaryKey": false, "notNull": true}, "organization_id": {"name": "organization_id", "type": "text", "primaryKey": false, "notNull": true}, "total_ai_message_count": {"name": "total_ai_message_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "last_used_at": {"name": "last_used_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "CURRENT_TIMESTAMP"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"project_ai_usage_project_id_project_id_fk": {"name": "project_ai_usage_project_id_project_id_fk", "tableFrom": "project_ai_usage", "tableTo": "project", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.project_asset": {"name": "project_asset", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "organization_id": {"name": "organization_id", "type": "text", "primaryKey": false, "notNull": true}, "project_id": {"name": "project_id", "type": "text", "primaryKey": false, "notNull": true}, "plan_id": {"name": "plan_id", "type": "text", "primaryKey": false, "notNull": true}, "attachment_key": {"name": "attachment_key", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"project_asset_project_id_project_id_fk": {"name": "project_asset_project_id_project_id_fk", "tableFrom": "project_asset", "tableTo": "project", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.subscription_limit": {"name": "subscription_limit", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "organization_id": {"name": "organization_id", "type": "text", "primaryKey": false, "notNull": true}, "stripe_customer_id": {"name": "stripe_customer_id", "type": "text", "primaryKey": false, "notNull": false}, "plan_name": {"name": "plan_name", "type": "text", "primaryKey": false, "notNull": true}, "plan_id": {"name": "plan_id", "type": "text", "primaryKey": false, "notNull": true}, "ai_nums": {"name": "ai_nums", "type": "integer", "primaryKey": false, "notNull": true}, "enhance_nums": {"name": "enhance_nums", "type": "integer", "primaryKey": false, "notNull": true}, "upload_limit": {"name": "upload_limit", "type": "integer", "primaryKey": false, "notNull": true}, "deploy_limit": {"name": "deploy_limit", "type": "integer", "primaryKey": false, "notNull": true}, "seats": {"name": "seats", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}, "project_nums": {"name": "project_nums", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "period_start": {"name": "period_start", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "period_end": {"name": "period_end", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"subscription_limit_org_plan_active_idx": {"name": "subscription_limit_org_plan_active_idx", "columns": [{"expression": "organization_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "plan_name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "where": "\"subscription_limit\".\"is_active\" = true", "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}