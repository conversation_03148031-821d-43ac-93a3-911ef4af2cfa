{"name": "@libra/ui", "version": "1.0.0", "type": "module", "private": true, "scripts": {"clean": "git clean -xdf .turbo node_modules", "format": "biome format .", "lint": "biome check .", "update": "bun update"}, "dependencies": {"@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-radio-group": "^1.3.7", "@tailwindcss/postcss": "^4.1.11", "class-variance-authority": "^0.7.1", "cmdk": "^0.2.1", "input-otp": "^1.4.2", "lucide-react": "^0.486.0", "next-themes": "^0.4.6", "radix-ui": "^1.4.2", "react-day-picker": "^9.8.1", "react-hook-form": "^7.61.1", "sonner": "^2.0.6", "tw-animate-css": "^1.3.6"}, "devDependencies": {"@libra/typescript-config": "*", "date-fns": "^4.1.0", "tailwind-merge": "^2.6.0", "clsx": "^2.1.1", "postcss": "^8.5.6", "autoprefixer": "^10.4.21", "tailwindcss": "^4.1.11", "tailwindcss-animate": "^1.0.7", "tailwind-scrollbar": "^4.0.2"}, "exports": {"./globals.css": "./src/styles/globals.css", "./postcss.config": "./postcss.config.mjs", "./lib/*": "./src/lib/*.ts", "./components/*": "./src/components/*.tsx"}}