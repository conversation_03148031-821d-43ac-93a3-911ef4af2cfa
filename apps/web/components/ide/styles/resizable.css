/*
 * SPDX-License-Identifier: AGPL-3.0-only
 * resizable.css
 * Copyright (C) 2025 Nextify Limited
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as
 * published by the Free Software Foundation, either version 3 of the
 * License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 *
 */

/* Resizable panel styles */

/* Ensure the resize handle stays visible and blue during drag */
body.resizing-panels {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* Maintain resize cursor during drag operation */
body.resizing-panels,
body.resizing-panels * {
  cursor: col-resize !important;
}

/* For vertical resizing */
body.resizing-panels[data-panel-direction="vertical"],
body.resizing-panels[data-panel-direction="vertical"] * {
  cursor: row-resize !important;
}

/* Ensure the blue line stays visible during drag */
.resizing-panels [data-resize-handle-state="drag"]::after {
  width: 3px !important;
  background-color: rgb(59 130 246) !important;
  opacity: 1 !important;
}

/* For vertical panels */
.resizing-panels [data-resize-handle-state="drag"][data-panel-group-direction="vertical"]::after {
  height: 3px !important;
  width: 100% !important;
}

/* Smooth panel transitions */
[data-panel] {
  transition: flex-basis 250ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* Disable transitions during resize */
.resizing-panels [data-panel] {
  transition: none !important;
}

/* Panel content fade in/out during resize - more subtle */
[data-panel] > * {
  transition: opacity 200ms ease-in-out;
}

.resizing-panels [data-panel] > * {
  opacity: 0.95;
}

/* Enhanced resize handle visual feedback */
[data-resize-handle] {
  transition: all 200ms ease-in-out;
}

[data-resize-handle]:hover {
  background-color: rgba(0, 0, 0, 0.02);
}

.dark [data-resize-handle]:hover {
  background-color: rgba(255, 255, 255, 0.02);
}

[data-resize-handle-state="drag"] {
  background-color: rgba(0, 0, 0, 0.05);
}

.dark [data-resize-handle-state="drag"] {
  background-color: rgba(255, 255, 255, 0.05);
}