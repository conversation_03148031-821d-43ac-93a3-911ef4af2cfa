/*
 * SPDX-License-Identifier: AGPL-3.0-only
 * docs-code-block.tsx
 * Copyright (C) 2025 Nextify Limited
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as
 * published by the Free Software Foundation, either version 3 of the
 * License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 *
 */

'use client'
import { Check, Copy } from 'lucide-react'
import {
  type ButtonHTMLAttributes,
  type HTMLAttributes,
  type ReactNode,
  forwardRef,
  useCallback,
  useRef,
} from 'react'

import type { ScrollAreaViewportProps } from '@radix-ui/react-scroll-area'
import { cn } from '@libra/ui/lib/utils'
import { ScrollArea, ScrollBar, ScrollViewport } from '@libra/ui/components/scroll-area'
import { buttonVariants } from '@libra/ui/components/button'
import { useClipboard } from '@/lib/hooks/use-clipboard'
import Frame from './frame'

export type CodeBlockProps = HTMLAttributes<HTMLElement> & {
  /**
   * Icon of code block
   *
   * When passed as a string, it assumes the value is the HTML of icon
   */
  icon?: ReactNode

  /**
   * Allow to copy code with copy button
   *
   * @defaultValue true
   */
  allowCopy?: boolean

  /**
   * Keep original background color generated by Shiki or Rehype Code
   *
   * @defaultValue false
   */
  keepBackground?: boolean

  viewportProps?: ScrollAreaViewportProps
}

export const Pre = forwardRef<HTMLPreElement, HTMLAttributes<HTMLPreElement>>(
  ({ className, ...props }, ref) => {
    return (
      <pre
        ref={ref}
        className={cn('p-4 focus-visible:outline-none', className)}
        {...props}
      >
        {props.children}
      </pre>
    )
  }
)

Pre.displayName = 'Pre'

export const CodeBlock = forwardRef<HTMLElement, CodeBlockProps>(
  (
    {
      title,
      allowCopy = true,
      keepBackground = false,
      icon,
      viewportProps,
      ...props
    },
    ref
  ) => {
    const [isCopied, copy] = useClipboard()
    const areaRef = useRef<HTMLDivElement>(null)
    const onCopy = useCallback(() => {
      const pre = areaRef.current?.getElementsByTagName('pre').item(0)

      if (!pre) return

      const clone = pre.cloneNode(true) as HTMLElement
      clone.querySelectorAll('.nd-copy-ignore').forEach((node) => {
        node.remove()
      })

      copy(clone.textContent ?? '')
    }, [copy])

    return (
      <Frame
        classNames={{
          wrapper: 'my-6 w-full',
        }}
      >
        <figure
          ref={ref}
          {...props}
          className={cn(
            'not-prose group relative text-xs',
            keepBackground &&
              'bg-[var(--shiki-light-bg)] dark:bg-[var(--shiki-dark-bg)]',
            props.className
          )}
        >
          {title ? (
            <div className="-100 flex h-8 flex-row items-center gap-2 border-b px-3 py-1">
              {icon ? (
                <div
                  className="text-fg-300 [&_svg]:size-3"
                  dangerouslySetInnerHTML={
                    typeof icon === 'string'
                      ? {
                          __html: icon,
                        }
                      : undefined
                  }
                >
                  {typeof icon !== 'string' ? icon : null}
                </div>
              ) : null}
              <figcaption className="text-fd-muted-foreground flex-1 truncate">
                {title}
              </figcaption>
              {allowCopy ? (
                <CopyButton
                  className="-me-2"
                  onCopy={onCopy}
                  isCopied={isCopied}
                />
              ) : null}
            </div>
          ) : (
            allowCopy && (
              <CopyButton
                className="absolute top-2 right-2 z-[2] backdrop-blur-md"
                onCopy={onCopy}
                isCopied={isCopied}
              />
            )
          )}
          <ScrollArea ref={areaRef} dir="ltr">
            <ScrollViewport
              {...viewportProps}
              className={cn(
                'max-h-[600px] max-w-full',
                viewportProps?.className
              )}
            >
              {props.children}
            </ScrollViewport>
            <ScrollBar orientation="horizontal" />
            <ScrollBar orientation="vertical" />
          </ScrollArea>
        </figure>
      </Frame>
    )
  }
)

CodeBlock.displayName = 'CodeBlock'

function CopyButton({
  className,
  onCopy,
  isCopied,
  ...props
}: ButtonHTMLAttributes<HTMLButtonElement> & {
  onCopy: () => void
  isCopied: boolean
}) {
  return (
    <button
      type="button"
      className={cn(
        buttonVariants({
          variant: 'ghost',
        }),
        'transition-opacity group-hover:opacity-100 [&_svg]:size-3.5',
        !isCopied && '[@media(hover:hover)]:opacity-0',
        className
      )}
      aria-label={isCopied ? 'Copied Text' : 'Copy Text'}
      onClick={onCopy}
      {...props}
    >
      <Check className={cn('transition-transform', !isCopied && 'scale-0')} />
      <Copy
        className={cn('absolute transition-transform', isCopied && 'scale-0')}
      />
    </button>
  )
}
